---
title: '🗨️ Discourse'
---

You can now easily load data from your community built with [Discourse](https://discourse.org/).

## Example

1. Setup the Discourse Loader with your community url.
```Python
from embedchain.loaders.discourse import DiscourseLoader

dicourse_loader = DiscourseLoader(config={"domain": "https://community.openai.com"})
```

2. Once you setup the loader, you can create an app and load data using the above discourse loader
```Python
import os
from embedchain.pipeline import Pipeline as App

os.environ["OPENAI_API_KEY"] = "sk-xxx"

app = App()

app.add("openai after:2023-10-1", data_type="discourse", loader=dicourse_loader)

question = "Where can I find the OpenAI API status page?"
app.query(question)
# Answer: You can find the OpenAI API status page at https:/status.openai.com/.
```

NOTE: The `add` function of the app will accept any executable search query to load data. Refer [Discourse API Docs](https://docs.discourse.org/#tag/Search) to learn more about search queries.

3. We automatically create a chunker to chunk your discourse data, however if you wish to provide your own chunker class. Here is how you can do that:
```Python

from embedchain.chunkers.discourse import DiscourseChunker
from embedchain.config.add_config import ChunkerConfig

discourse_chunker_config = ChunkerConfig(chunk_size=1000, chunk_overlap=0, length_function=len)
discourse_chunker = DiscourseChunker(config=discourse_chunker_config)

app.add("openai", data_type='discourse', loader=dicourse_loader, chunker=discourse_chunker)
```