---
title: Memory Customization
description: 'Mem0 supports customizing the memories you store, allowing you to focus on pertinent information while omitting irrelevant data.'
icon: "filter"
iconType: "solid"
---

<Snippet file="paper-release.mdx" />

## Benefits of Memory Customization

Memory customization offers several key benefits:

• **Focused Storage**: Store only relevant information for a streamlined system.

• **Improved Accuracy**: Curate memories for more accurate and relevant retrieval.

• **Enhanced Privacy**: Exclude sensitive information for better privacy control.

• **Resource Efficiency**: Optimize storage and processing by keeping only pertinent data.

• **Personalization**: Tailor the experience to individual user preferences.

• **Contextual Relevance**: Improve effectiveness in specialized domains or applications.

These benefits allow users to fine-tune their memory systems, creating a more powerful and personalized AI assistant experience.


## Memory Inclusion
Users can define specific kinds of memories to store. This feature enhances memory management by focusing on relevant information, resulting in a more efficient and personalized experience.
Here’s how you can do it:

```python
import os
from mem0 import MemoryClient

os.environ["MEM0_API_KEY"] = "your-api-key"

m = MemoryClient()

# Define what to include
includes = "sports related things"

messages = [
    {"role": "user", "content": "Hi, my name is <PERSON> and I love to play badminton"},
    {"role": "assistant", "content": "Nice to meet you, Alice! Badminton is a great sport."},
    {"role": "user", "content": "I love music festivals"},
    {"role": "assistant", "content": "Music festivals are exciting! Do you have a favorite one?"},
    {"role": "user", "content": "I love eating spicy food"},
    {"role": "assistant", "content": "Spicy food is delicious! What's your favorite spicy dish?"},
    {"role": "user", "content": "I love playing baseball with my friends"},
    {"role": "assistant", "content": "Baseball with friends sounds fun!"},
]
```

<CodeGroup>
```python Code
client.add(messages, user_id="alice", includes=includes)
```

```json Stored Memories
User's name is Alice.
Alice loves to play badminton.
User loves playing baseball with friends.
```
</CodeGroup>




## Memory Exclusion

In addition to specifying what to include, users can also define exclusion rules for their memory management. This feature allows for fine-tuning the memory system by instructing it to omit certain types of information.
Here’s how you can do it:

```python
from mem0 import MemoryClient

m = MemoryClient(api_key="xxx")

# Define what to exclude
excludes = "food preferences"

messages = [
    {"role": "user", "content": "Hi, my name is Alice and I love to play badminton"},
    {"role": "assistant", "content": "Nice to meet you, Alice! Badminton is a great sport."},
    {"role": "user", "content": "I love music festivals"},
    {"role": "assistant", "content": "Music festivals are exciting! Do you have a favorite one?"},
    {"role": "user", "content": "I love eating spicy food"},
    {"role": "assistant", "content": "Spicy food is delicious! What's your favorite spicy dish?"},
    {"role": "user", "content": "I love playing baseball with my friends"},
    {"role": "assistant", "content": "Baseball with friends sounds fun!"},
]
```

<CodeGroup>
```python Code
client.add(messages, user_id="alice", excludes=excludes)
```

```json Stored Memories
User's name is Alice.
Alice loves to play badminton.
Loves music festivals.
User loves playing baseball with friends.
```
</CodeGroup>



If you have any questions, please feel free to reach out to us using one of the following methods:

<Snippet file="get-help.mdx" />