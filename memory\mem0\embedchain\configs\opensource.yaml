app:
  config:
    id: 'open-source-app'
    collect_metrics: false

llm:
  provider: gpt4all
  config:
    model: 'orca-mini-3b-gguf2-q4_0.gguf'
    temperature: 0.5
    max_tokens: 1000
    top_p: 1
    stream: false

vectordb:
  provider: chroma
  config:
    collection_name: 'open-source-app'
    dir: db
    allow_reset: true

embedder:
  provider: gpt4all
  config:
    deployment_name: 'test-deployment'
