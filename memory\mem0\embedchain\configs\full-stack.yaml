app:
  config:
    id: 'full-stack-app'

chunker:
  chunk_size: 100
  chunk_overlap: 20
  length_function: 'len'

llm:
  provider: openai
  config:
    model: 'gpt-4o-mini'
    temperature: 0.5
    max_tokens: 1000
    top_p: 1
    stream: false
    prompt: |
      Use the following pieces of context to answer the query at the end.
      If you don't know the answer, just say that you don't know, don't try to make up an answer.

      $context

      Query: $query

      Helpful Answer:
    system_prompt: |
      Act as <PERSON>. Answer the following questions in the style of <PERSON>.

vectordb:
  provider: chroma
  config:
    collection_name: 'my-collection-name'
    dir: db
    allow_reset: true

embedder:
  provider: openai
  config:
    model: 'text-embedding-ada-002'
