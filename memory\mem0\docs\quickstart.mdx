---
title: Quickstart
icon: "bolt"
iconType: "solid"
---

<Snippet file="paper-release.mdx" />
<Note type="info">
🎉 We're excited to announce that Claude 4 is now available with Mem0! Check it out [here](components/llms/models/anthropic).
</Note>


Mem0 offers two powerful ways to leverage our technology: [our managed platform](#mem0-platform-managed-solution) and [our open source solution](#mem0-open-source).

Check out our [Playground](https://mem0.dev/pd-pg) to see Mem0 in action.

<CardGroup cols={2}>
  <Card title="Mem0 Platform (Managed Solution)" icon="chart-simple" href="#mem0-platform-managed-solution">
    Better, faster, fully managed, and hassle free solution.
  </Card>
  <Card title="Mem0 Open Source" icon="code-branch" href="#mem0-open-source">
    Self hosted, fully customizable, and open source.
  </Card>
</CardGroup>


## Mem0 Platform (Managed Solution)

Our fully managed platform provides a hassle-free way to integrate Mem0's capabilities into your AI agents and assistants. Sign up for Mem0 platform [here](https://mem0.dev/pd).

The Mem0 SDK supports both Python and JavaScript, with full [TypeScript](/platform/quickstart/#4-11-working-with-mem0-in-typescript) support as well.

Follow the steps below to get started with Mem0 Platform:

1. [Install Mem0](#1-install-mem0)
2. [Add Memories](#2-add-memories)
3. [Retrieve Memories](#3-retrieve-memories)

### 1. Install Mem0

<AccordionGroup>
<Accordion title="Install package">
<CodeGroup>
```bash pip
pip install mem0ai
```

```bash npm
npm install mem0ai
```
</CodeGroup>
</Accordion>
<Accordion title="Get API Key">

1. Sign in to [Mem0 Platform](https://mem0.dev/pd-api)
2. Copy your API Key from the dashboard

![Get API Key from Mem0 Platform](/images/platform/api-key.png)

</Accordion>
</AccordionGroup>

### 2. Add Memories

<AccordionGroup>
<Accordion title="Instantiate client">
<CodeGroup>
```python Python
import os
from mem0 import MemoryClient

os.environ["MEM0_API_KEY"] = "your-api-key"

client = MemoryClient()
```

```javascript JavaScript
import MemoryClient from 'mem0ai';
const client = new MemoryClient({ apiKey: 'your-api-key' });
```
</CodeGroup>
</Accordion>
<Accordion title="Add memories">
<CodeGroup>

```python Python
messages = [
    {"role": "user", "content": "Thinking of making a sandwich. What do you recommend?"},
    {"role": "assistant", "content": "How about adding some cheese for extra flavor?"},
    {"role": "user", "content": "Actually, I don't like cheese."},
    {"role": "assistant", "content": "I'll remember that you don't like cheese for future recommendations."}
]
client.add(messages, user_id="alex")
```

```javascript JavaScript
const messages = [
    {"role": "user", "content": "Thinking of making a sandwich. What do you recommend?"},
    {"role": "assistant", "content": "How about adding some cheese for extra flavor?"},
    {"role": "user", "content": "Actually, I don't like cheese."},
    {"role": "assistant", "content": "I'll remember that you don't like cheese for future recommendations."}
];
client.add(messages, { user_id: "alex" })
    .then(response => console.log(response))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "messages": [
             {"role": "user", "content": "I live in San Francisco. Thinking of making a sandwich. What do you recommend?"},
             {"role": "assistant", "content": "How about adding some cheese for extra flavor?"},
             {"role": "user", "content": "Actually, I don't like cheese."},
             {"role": "assistant", "content": "I'll remember that you don't like cheese for future recommendations."}
         ],
         "user_id": "alex"
     }'
```

```json Output
[
  {
    "id": "24e466b5-e1c6-4bde-8a92-f09a327ffa60",
    "memory": "Does not like cheese",
    "event": "ADD"
  },
  {
    "id": "e8d78459-fadd-4c5a-bece-abb8c3dc7ed7",
    "memory": "Lives in San Francisco",
    "event": "ADD"
  }
]
```
</CodeGroup>
</Accordion>
</AccordionGroup>

### 3. Retrieve Memories

<AccordionGroup>
<Accordion title="Search for relevant memories">
<CodeGroup>

```python Python
# Example showing location and preference-aware recommendations
query = "I'm craving some pizza. Any recommendations?"
filters = {
    "AND": [
        {
            "user_id": "alex"
        }
    ]
}
client.search(query, version="v2", filters=filters)
```

```javascript JavaScript
const query = "I'm craving some pizza. Any recommendations?";
const filters = {
    "AND": [
        {
            "user_id": "alex"
        }
    ]
};
client.search(query, { version: "v2", filters })
    .then(results => console.log(results))
    .catch(error => console.error(error));
```

```bash cURL
curl -X POST "https://api.mem0.ai/v1/memories/search/?version=v2" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "query": "I'm craving some pizza. Any recommendations?",
         "filters": {
             "AND": [
                 {
                    "user_id": "alex"
                 }
             ]
         }
     }'
```

```json Output
[
    {
        "id": "7f165f7e-b411-4afe-b7e5-35789b72c4a5",
        "memory": "Does not like cheese",
        "user_id": "alex",
        "metadata": null,
        "created_at": "2024-07-20T01:30:36.275141-07:00",
        "updated_at": "2024-07-20T01:30:36.275172-07:00",
        "score": 0.92
    },
    {
        "id": "8f165f7e-b411-4afe-b7e5-35789b72c4b6",
        "memory": "Lives in San Francisco",
        "user_id": "alex",
        "metadata": null,
        "created_at": "2024-07-20T01:30:36.275141-07:00",
        "updated_at": "2024-07-20T01:30:36.275172-07:00",
        "score": 0.85
    }
]
```
</CodeGroup>

</Accordion>
<Accordion title="Get all memories of a user">

<CodeGroup>

```python Python
filters = {
   "AND": [
      {
         "user_id": "alex"
      }
   ]
}

all_memories = client.get_all(version="v2", filters=filters, page=1, page_size=50)
```

```javascript JavaScript
const filters = {
   "AND": [
      {
         "user_id": "alex"
      }
   ]
};

client.getAll({ version: "v2", filters, page: 1, page_size: 50 })
    .then(memories => console.log(memories))
    .catch(error => console.error(error));
```

```bash cURL
curl -X GET "https://api.mem0.ai/v1/memories/?version=v2&page=1&page_size=50" \
     -H "Authorization: Token your-api-key" \
     -H "Content-Type: application/json" \
     -d '{
         "filters": {
             "AND": [
                 {
                     "user_id": "alice"
                 }
             ]
         }
     }'
```

```json Output
[
    {
        "id": "7f165f7e-b411-4afe-b7e5-35789b72c4a5",
        "memory": "Does not like cheese",
        "user_id": "alex",
        "metadata": null,
        "created_at": "2024-07-20T01:30:36.275141-07:00",
        "updated_at": "2024-07-20T01:30:36.275172-07:00",
        "score": 0.92
    },
    {
        "id": "8f165f7e-b411-4afe-b7e5-35789b72c4b6",
        "memory": "Lives in San Francisco",
        "user_id": "alex",
        "metadata": null,
        "created_at": "2024-07-20T01:30:36.275141-07:00",
        "updated_at": "2024-07-20T01:30:36.275172-07:00",
        "score": 0.85
    }
]
```
</CodeGroup>
</Accordion>
</AccordionGroup>

<Card title="Mem0 Platform" icon="chart-simple" href="/platform/overview">
  Learn more about Mem0 platform
</Card>

## Mem0 Open Source

Our open-source version is available for those who prefer full control and customization. You can self-host Mem0 on your infrastructure and integrate it with your AI agents and assistants. Checkout our [GitHub repository](https://mem0.dev/gd)

Follow the steps below to get started with Mem0 Open Source:

1. [Install Mem0 Open Source](#1-install-mem0-open-source)
2. [Add Memories](#2-add-memories-open-source)
3. [Retrieve Memories](#3-retrieve-memories-open-source)

### 1. Install Mem0 Open Source

<AccordionGroup>
<Accordion title="Install package">
<CodeGroup>
```bash pip
pip install mem0ai
```

```bash npm
npm install mem0ai
```
</CodeGroup>
</Accordion>
</AccordionGroup>

### 2. Add Memories <a name="2-add-memories-open-source"></a>

<AccordionGroup>
<Accordion title="Instantiate client">
<CodeGroup>
```python Python
from mem0 import Memory
m = Memory()
```

```typescript TypeScript
import { Memory } from 'mem0ai/oss';
const memory = new Memory();
```
</CodeGroup>
</Accordion>
<Accordion title="Add memories">
<CodeGroup>
```python Code
# For a user
result = m.add("I like to drink coffee in the morning and go for a walk.", user_id="alice", metadata={"category": "preferences"})
```

```typescript TypeScript
const result = memory.add("I like to drink coffee in the morning and go for a walk.", { userId: "alice", metadata: { category: "preferences" } });
```

```json Output
[
    {
        "id": "3dc6f65f-fb3f-4e91-89a8-ed1a22f8898a",
        "data": {"memory": "Likes to drink coffee in the morning"},
        "event": "ADD"
    },
    {
        "id": "f1673706-e3d6-4f12-a767-0384c7697d53",
        "data": {"memory": "Likes to go for a walk"},
        "event": "ADD"
    }
]
```
</CodeGroup>
</Accordion>
</AccordionGroup>

### 3. Retrieve Memories <a name="3-retrieve-memories-open-source"></a>

<AccordionGroup>
<Accordion title="Search for relevant memories">
<CodeGroup>
```python Python
related_memories = m.search("Should I drink coffee or tea?", user_id="alice")
```

```typescript TypeScript
const relatedMemories = memory.search("Should I drink coffee or tea?", { userId: "alice" });
```

```json Output
[
    {
        "id": "3dc6f65f-fb3f-4e91-89a8-ed1a22f8898a",
        "memory": "Likes to drink coffee in the morning",
        "user_id": "alice",
        "metadata": {"category": "preferences"},
        "categories": ["user_preferences", "food"],
        "immutable": false,
        "created_at": "2025-02-24T20:11:39.010261-08:00",
        "updated_at": "2025-02-24T20:11:39.010274-08:00",
        "score": 0.5915589089130715
    },
    {
        "id": "e8d78459-fadd-4c5a-bece-abb8c3dc7ed7",
        "memory": "Likes to go for a walk",
        "user_id": "alice",
        "metadata": {"category": "preferences"},
        "categories": ["hobby", "food"],
        "immutable": false,
        "created_at": "2025-02-24T11:47:52.893038-08:00",
        "updated_at": "2025-02-24T11:47:52.893048-08:00",
        "score": 0.*****************
    }
]
```
</CodeGroup>

</Accordion>
</AccordionGroup>

<CardGroup cols={2}>
<Card title="Mem0 OSS Python SDK" icon="python" href="/open-source/python-quickstart">
  Learn more about Mem0 OSS Python SDK
</Card>
<Card title="Mem0 OSS Node.js SDK" icon="node" href="/open-source-typescript/quickstart">
  Learn more about Mem0 OSS Node.js SDK
</Card>
</CardGroup>