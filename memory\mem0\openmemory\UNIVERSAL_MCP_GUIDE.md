# Universal MCP Server Guide

## Overview

The OpenMemory MCP server now supports **multiple MCP clients** and is no longer restricted to just Cursor IDE. The server can automatically detect and work with any MCP-compatible client.

## 🎯 **Key Features**

- ✅ **Universal MCP client support** (cursor, claude, vscode, jetbrains, neovim, etc.)
- ✅ **Automatic client detection** from request headers and user agents
- ✅ **Backward compatibility** with existing Cursor IDE integration
- ✅ **Custom client support** via headers
- ✅ **No configuration changes required** for existing setups

## 🔗 **Supported Endpoints**

### Universal Endpoints (Recommended)
```
GET  /mcp/sse/{user_id}                    # SSE connection for any client
POST /mcp/messages/                        # Message handling for any client
POST /mcp/sse/{user_id}/messages/          # SSE messages for any client
```

### Legacy Endpoints (Backward Compatibility)
```
GET  /mcp/{client_name}/sse/{user_id}      # Original Cursor-specific endpoint
POST /mcp/{client_name}/sse/{user_id}/messages/  # Original message endpoint
```

## 🤖 **Supported MCP Clients**

### Auto-Detected Clients
The server automatically detects these clients from User-Agent headers:

| Client | Detection Pattern | Example User-Agent |
|--------|------------------|-------------------|
| **Cursor IDE** | `cursor` | `Cursor/1.0` |
| **Claude Desktop** | `claude`, `anthropic` | `Claude Desktop/1.0` |
| **VS Code** | `vscode` | `Visual Studio Code/1.85.0` |
| **JetBrains IDEs** | `jetbrains`, `intellij` | `IntelliJ IDEA/2023.3` |
| **Neovim** | `neovim`, `nvim` | `Neovim/0.9.0` |
| **Emacs** | `emacs` | `Emacs/29.1` |
| **Sublime Text** | `sublime` | `Sublime Text/4` |

### Custom Clients
For custom MCP clients, use these headers:
```http
x-mcp-client: your-client-name
# OR
mcp-client: your-client-name
```

## 🚀 **Usage Examples**

### 1. Claude Desktop Configuration
```json
{
  "mcpServers": {
    "openmemory": {
      "command": "curl",
      "args": ["-X", "GET", "http://localhost:8765/mcp/sse/your-user-id"],
      "env": {}
    }
  }
}
```

### 2. VS Code MCP Extension
```json
{
  "mcp.servers": [
    {
      "name": "openmemory",
      "url": "http://localhost:8765/mcp/sse/your-user-id",
      "headers": {
        "x-mcp-client": "vscode-mcp-extension"
      }
    }
  ]
}
```

### 3. Custom MCP Client (Python Example)
```python
import requests

# Connect to universal endpoint
headers = {
    'User-Agent': 'MyCustomClient/1.0',
    'x-mcp-client': 'my-custom-client'
}

response = requests.get(
    'http://localhost:8765/mcp/sse/my-user-id',
    headers=headers,
    stream=True
)
```

### 4. Cursor IDE (Existing Configuration - No Changes Needed)
```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_API_URL": "http://localhost:8765/mcp/cursor/sse/User"
      }
    }
  }
}
```

## 🔧 **Migration Guide**

### For Existing Cursor Users
**No action required!** Your existing configuration will continue to work exactly as before.

### For New MCP Clients
1. Use the universal endpoint: `/mcp/sse/{your-user-id}`
2. Set appropriate User-Agent or custom headers
3. Connect using standard MCP protocol

## 🛠️ **Technical Details**

### Client Detection Logic
1. **User-Agent Header**: Checks for known client patterns
2. **Custom Headers**: `x-mcp-client` or `mcp-client`
3. **Fallback**: Defaults to `mcp-client` for unknown clients

### Backward Compatibility
- All existing Cursor IDE integrations continue to work
- Legacy endpoints remain fully functional
- No breaking changes to existing APIs

### Logging
The server logs client detection for debugging:
```
🌐 Universal SSE Connection - uid: 'User', detected_client: 'claude'
🌐 SSE Connection established - uid: 'User', client_name: 'claude'
```

## 🧪 **Testing Your Client**

Test your MCP client connection:
```bash
# Test universal endpoint
curl -H "User-Agent: YourClient/1.0" \
     -H "x-mcp-client: your-client" \
     http://localhost:8765/mcp/sse/TestUser

# Check server logs
docker logs openmemory-openmemory-mcp-1 --tail 10
```

## 📞 **Support**

- **Existing Cursor users**: No changes needed, everything works as before
- **New MCP clients**: Use universal endpoints with proper headers
- **Issues**: Check server logs for client detection and connection details

---

**The OpenMemory MCP server is now truly universal! 🌍**
