---
title: 'Gradio.app'
description: 'Deploy your RAG application to gradio.app platform'
---

Embedchain offers a Streamlit template to facilitate the development of RAG chatbot applications in just three easy steps.

Follow the instructions given below to deploy your first application quickly:

## Step-1: Create RAG app

We provide a command line utility called `ec` in embedchain that inherits the template for `gradio.app` platform and help you deploy the app. Follow the instructions to create a gradio.app app using the template provided:

```bash Install embedchain
pip install embedchain
```

```bash Create application
mkdir my-rag-app
ec create --template=gradio.app
```

This will generate a directory structure like this:

```bash
├── app.py
├── embedchain.json
└── requirements.txt
```

Feel free to edit the files as required.
- `app.py`: Contains API app code
- `embedchain.json`: Contains embedchain specific configuration for deployment (you don't need to configure this)
- `requirements.txt`: Contains python dependencies for your application

## Step-2: Test app locally

You can run the app locally by simply doing:

```bash Run locally
pip install -r requirements.txt
ec dev
```

## Step-3: Deploy to gradio.app

```bash Deploy to gradio.app
ec deploy
```

This will run `gradio deploy` which will prompt you questions and deploy your app directly to huggingface spaces.

<img src="/images/gradio_app.png" alt="gradio app" />

## Seeking help?

If you run into issues with deployment, please feel free to reach out to us via any of the following methods:

<Snippet file="get-help.mdx" />
